import { handleError } from "../../../utils/helpers";
import { getOrderBy } from "../../../utils/sortHelper";

export const viewLegrandMapping = async (req, res) => {
  try {
    const {
      page,
      pageSize,
      businessUnit,
      legalName,
      customeCode,
      shippingBillingName,
      shippingBillingAddress,
      location,
      zipPostal,
      aliasCity,
      aliasShippingNames,
      sortBy = "createdAt",
      order = "desc",
    } = req.query;

    const take = Number(pageSize);
    const skip = (Number(page) - 1) * Number(pageSize);
    const whereClause: { AND?: any[] } = { AND: [] };

    const searchConditions: any[] = [];

    if (businessUnit) {
      const businessUnitList = businessUnit
        .split(",")
        .map((item: any) => item.trim());
      searchConditions.push({
        OR: businessUnitList.map((businessUnit: any) => ({
          businessUnit: {
            contains: businessUnit,
            mode: "insensitive",
          },
        })),
      });
    }

    if (legalName) {
      const legalNameList = legalName
        .split(",")
        .map((item: any) => item.trim());
      searchConditions.push({
        OR: legalNameList.map((legalName: any) => ({
          legalName: {
            contains: legalName,
            mode: "insensitive",
          },
        })),
      });
    }

    if (customeCode) {
      const customeCodeList = customeCode
        .split(",")
        .map((item: any) => item.trim());
      searchConditions.push({
        OR: customeCodeList.map((customeCode: any) => ({
          customeCode: {
            contains: customeCode,
            mode: "insensitive",
          },
        })),
      });
    }

    if (shippingBillingName) {
      const shippingBillingNameList = shippingBillingName
        .split(",")
        .map((item: any) => item.trim());
      searchConditions.push({
        OR: shippingBillingNameList.map((shippingBillingName: any) => ({
          shippingBillingName: {
            contains: shippingBillingName,
            mode: "insensitive",
          },
        })),
      });
    }

    if (shippingBillingAddress) {
      const shippingBillingAddressList = shippingBillingAddress
        .split(",")
        .map((item: any) => item.trim());
      searchConditions.push({
        OR: shippingBillingAddressList.map((shippingBillingAddress: any) => ({
          shippingBillingAddress: {
            contains: shippingBillingAddress,
            mode: "insensitive",
          },
        })),
      });
    }

    if (location) {
      const locationList = location.split(",").map((item: any) => item.trim());
      searchConditions.push({
        OR: locationList.map((location: any) => ({
          location: {
            contains: location,
            mode: "insensitive",
          },
        })),
      });
    }

    if (zipPostal) {
      const zipPostalList = zipPostal
        .split(",")
        .map((item: any) => item.trim());
      searchConditions.push({
        OR: zipPostalList.map((zipPostal: any) => ({
          zipPostal: {
            contains: zipPostal,
            mode: "insensitive",
          },
        })),
      });
    }

    if (aliasCity) {
      const aliasCityList = aliasCity
        .split(",")
        .map((item: any) => item.trim());
      searchConditions.push({
        OR: aliasCityList.map((aliasCity: any) => ({
          aliasCity: {
            contains: aliasCity,
            mode: "insensitive",
          },
        })),
      });
    }

    if (aliasShippingNames) {
      const aliasShippingNamesList = aliasShippingNames
        .split(",")
        .map((item: any) => item.trim());
      searchConditions.push({
        OR: aliasShippingNamesList.map((aliasShippingNames: any) => ({
          aliasShippingNames: {
            contains: aliasShippingNames,
            mode: "insensitive",
          },
        })),
      });
    }

    if (searchConditions.length > 0) {
      whereClause.AND.push({
        AND: searchConditions, // Add all the conditions under OR
      });
    }

    const fieldMapping = {
      businessUnit: "businessUnit",
      legalName: "legalName",
      customeCode: "customeCode",
      shippingBillingName: "shippingBillingName",
      shippingBillingAddress: "shippingBillingAddress",
      location: "location",
      zipPostal: "zipPostal",
      aliasCity: "aliasCity",
      aliasShippingNames: "aliasShippingNames",
    };
    const sortByArr = String(sortBy)
      .split(",")
      .map((s) => s.trim());
    const orderArr = String(order)
      .split(",")
      .map((s) => s.trim());
    const mappedSortByArr = sortByArr.map(
      (field) => fieldMapping[field] || field
    );
    const allowedFields = [
      "businessUnit",
      "legalName",
      "customeCode",
      "shippingBillingName",
      "shippingBillingAddress",
      "location",
      "zipPostal",
      "aliasCity",
      "aliasShippingNames",
      "createdAt",
      "updatedAt",
    ];
    const orderBy = getOrderBy(mappedSortByArr, orderArr, allowedFields, "createdAt", "desc");
    const data = await prisma.legrandMapping.findMany({
      where: whereClause,
      take: page ? take : undefined,
      skip: page ? skip : undefined,
      orderBy,
    });

    const datalength = await prisma.legrandMapping.count({
      where: whereClause,
    });

    if (data.length > 0) {
      return res.status(200).json({ data, datalength });
    }
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewLegrandMappingById = async (req, res) => {
  try {
    const { id } = req.params;
    const data = await prisma.legrandMapping.findUnique({
      where: { id: id },
    });
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(404).json({ message: "Rule not found" });
  } catch (error) {
    return handleError(res, error);
  }
};
