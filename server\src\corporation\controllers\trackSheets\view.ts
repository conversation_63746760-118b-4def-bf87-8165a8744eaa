import { handleError } from "../../../utils/helpers";
import { getOrderBy } from "../../../utils/sortHelper";

export const viewTrackSheets = async (req, res) => {
  try {
    const clientId = req.params.id;
    if (!clientId) {
      return res.status(400).json({ error: "clientId is required" });
    }

    const {
      receivedDate_from,
      receivedDate_to,
      invoiceDate_from,
      invoiceDate_to,
      shipmentDate_from,
      shipmentDate_to,
      client,
      company,
      division,
      carrier,
      ftpFileName,
      ftpPage,
      MasterInvoice,
      invoice,
      bol,
      invoiceTotal,
      currency,
      qtyShipped,
      quantityBilledText,
      invoiceStatus,
      manualMatching,
      invoiceType,
      weightUnitName,
      savings,
      docAvailable,
      notes,
      Mistake,
      mistake,
      enteredBy,
      manifestStatus,
      manifestDate_from,
      manifestDate_to,
      actionRequired,
      manifestNotes,
      freightTerm,
      page,
      pageSize,
      sortBy = "id",
      order = "desc",
      systemGeneratedWarnings,
      hasTickets,
      ...customFieldParams
    } = req.query;    


    const take = Number(pageSize);
    const skip = (Number(page) - 1) * Number(pageSize);

    const whereClause: { AND?: any[] } = { AND: [] };

    const searchConditions: any[] = [];

   if (systemGeneratedWarnings === "true") {
  searchConditions.push({
    OR: [
      {
        systemGeneratedWarnings: {
          some: {},
        },
      },
      {
        invoiceWarnings: {
          not: null,
        },
      },
    ],
  });
} else if (systemGeneratedWarnings === "false") {
  searchConditions.push({
    AND: [
      {
        systemGeneratedWarnings: {
          none: {},
        },
      },
      {
        invoiceWarnings: null,
      },
    ],
  });
}


    if (client) {
      const clientList = client.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: clientList.map((clientName) => ({
          client: {
            client_name: {
              contains: clientName,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (company) {
      const companyList = company.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: companyList.map((company) => ({
          company: {
            contains: company,
            mode: "insensitive",
          },
        })),
      });
    }

    if (division) {
      const divisionList = division.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: divisionList.map((division) => ({
          division: {
            contains: division,
            mode: "insensitive",
          },
        })),
      });
    }

    if (carrier) {
      const carrierList = carrier.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: carrierList.map((carrier) => ({
          carrier: {
            name: {
              contains: carrier,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (ftpFileName) {
      const ftpFileNameList = ftpFileName.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: ftpFileNameList.map((ftpFileName) => ({
          ftpFileName: {
            contains: ftpFileName,
            mode: "insensitive",
          },
        })),
      });
    }

    if (ftpPage) {
      const ftpPageList = ftpPage.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: ftpPageList.map((ftpPages) => ({
          ftpPage: {
            contains: ftpPages,
            mode: "insensitive",
          },
        })),
      });
    }

    if (MasterInvoice) {
      const masterInvoiceList = MasterInvoice.split(",").map((item) =>
        item.trim()
      );
      searchConditions.push({
        OR: masterInvoiceList.map((masterInvoice) => ({
          masterInvoice: {
            contains: masterInvoice,
            mode: "insensitive",
          },
        })),
      });
    }

    if (invoice) {
      const invoiceList = invoice.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: invoiceList.map((invoice) => ({
          invoice: {
            contains: invoice,
            mode: "insensitive",
          },
        })),
      });
    }

    if (bol) {
      const bolList = bol.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: bolList.map((bol) => ({
          bol: {
            contains: bol,
            mode: "insensitive",
          },
        })),
      });
    }

    if (invoiceDate_from || invoiceDate_to) {
      const dateRangeFilter: any = {};
      if (invoiceDate_from) {
        const fromDate = new Date(invoiceDate_from);
        fromDate.setHours(0, 0, 0, 0);
        dateRangeFilter.gte = fromDate;
      }
      if (invoiceDate_to) {
        const toDate = new Date(invoiceDate_to);
        toDate.setHours(23, 59, 59, 999);
        dateRangeFilter.lte = toDate;
      }

      searchConditions.push({
        invoiceDate: dateRangeFilter,
      });
    }

    if (receivedDate_from || receivedDate_to) {
      const dateRangeFilter: any = {};
      if (receivedDate_from) {
        const fromDate = new Date(receivedDate_from);
        fromDate.setHours(0, 0, 0, 0);
        dateRangeFilter.gte = fromDate;
      }
      if (receivedDate_to) {
        const toDate = new Date(receivedDate_to);
        toDate.setHours(23, 59, 59, 999);
        dateRangeFilter.lte = toDate;
      }

      searchConditions.push({
        receivedDate: dateRangeFilter,
      });
    }

    if (shipmentDate_from || shipmentDate_to) {
      const dateRangeFilter: any = {};
      if (shipmentDate_from) {
        const fromDate = new Date(shipmentDate_from);
        fromDate.setHours(0, 0, 0, 0);
        dateRangeFilter.gte = fromDate;
      }
      if (shipmentDate_to) {
        const toDate = new Date(shipmentDate_to);
        toDate.setHours(23, 59, 59, 999);
        dateRangeFilter.lte = toDate;
      }

      searchConditions.push({
        shipmentDate: dateRangeFilter,
      });
    }

    if (invoiceTotal) {
      const invoiceTotalList = invoiceTotal
        .split(",")
        .map((item) => item.trim());
      searchConditions.push({
        OR: invoiceTotalList.map((total) => {
          const rangeMatch = total.match(/^(\d+(?:\.\d+)?)\s*(?:-|to)\s*(\d+(?:\.\d+)?)$/i);
          
          if (rangeMatch) {
            const minValue = parseFloat(rangeMatch[1]);
            const maxValue = parseFloat(rangeMatch[2]);
            return {
              AND: [
                { invoiceTotal: { gte: minValue } },
                { invoiceTotal: { lte: maxValue } }
              ]
            };
          } else {
            const numericValue = parseFloat(total);
            if (!isNaN(numericValue)) {
              return { invoiceTotal: numericValue };
            } else {
              return {
                invoiceTotal: {
                  contains: total,
                  mode: "insensitive",
                },
              };
            }
          }
        }),
      });
    }

    if (currency) {
      const currencyList = currency.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: currencyList.map((currency) => ({
          currency: {
            contains: currency,
            mode: "insensitive",
          },
        })),
      });
    }

    if (qtyShipped) {
      const qtyShippedList = qtyShipped.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: qtyShippedList.map((qty) => {
          const rangeMatch = qty.match(/^(\d+)\s*(?:-|to)\s*(\d+)$/i);
          
          if (rangeMatch) {
            const minValue = parseInt(rangeMatch[1]);
            const maxValue = parseInt(rangeMatch[2]);
            return {
              AND: [
                { qtyShipped: { gte: minValue } },
                { qtyShipped: { lte: maxValue } }
              ]
            };
          } else {
            const numericValue = parseInt(qty);
            if (!isNaN(numericValue)) {
              return { qtyShipped: numericValue };
            } else {
              return null;
            }
          }
        }).filter(Boolean),
      });
    }

    if (quantityBilledText) {
      const quantityBilledTextList = quantityBilledText
        .split(",")
        .map((item) => item.trim());
      searchConditions.push({
        OR: quantityBilledTextList.map((quantityBilledText) => ({
          quantityBilledText: {
            contains: quantityBilledText,
            mode: "insensitive",
          },
        })),
      });
    }

    if (invoiceStatus) {
      const invoiceStatusList = invoiceStatus
        .split(",")
        .map((item) => item.trim());
      searchConditions.push({
        OR: invoiceStatusList.map((invoiceStatus) => ({
          invoiceStatus: {
            contains: invoiceStatus,
            mode: "insensitive",
          },
        })),
      });
    }

    if (manualMatching) {
      const manualMatchingList = manualMatching
        .split(",")
        .map((item) => item.trim());
      searchConditions.push({
        OR: manualMatchingList.map((manualMatching) => ({
          manualMatching: {
            contains: manualMatching,
            mode: "insensitive",
          },
        })),
      });
    }

    if (invoiceType) {
      const invoiceTypeList = invoiceType.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: invoiceTypeList.map((invoiceType) => ({
          invoiceType: {
            contains: invoiceType,
            mode: "insensitive",
          },
        })),
      });
    }

    if (weightUnitName) {
      const weightUnitNameList = weightUnitName
        .split(",")
        .map((item) => item.trim());
      searchConditions.push({
        OR: weightUnitNameList.map((weightUnitName) => ({
          weightUnitName: {
            contains: weightUnitName,
            mode: "insensitive",
          },
        })),
      });
    }

    if (savings) {
      const savingsList = savings.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: savingsList.map((savings) => ({
          savings: {
            contains: savings,
            mode: "insensitive",
          },
        })),
      });
    }

    if (docAvailable) {
      const docAvailableList = docAvailable
        .split(",")
        .map((item) => item.trim());
      searchConditions.push({
        OR: docAvailableList.map((docAvailable) => ({
          docAvailable: {
            contains: docAvailable,
            mode: "insensitive",
          },
        })),
      });
    }

    if (notes) {
      const notesList = notes.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: notesList.map((notes) => ({
          notes: {
            contains: notes,
            mode: "insensitive",
          },
        })),
      });
    }

    if (enteredBy) {
      const enteredByList = enteredBy.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: enteredByList.map((enteredBy) => ({
          enteredBy: {
            contains: enteredBy,
            mode: "insensitive",
          },
        })),
      });
    }

    if (manifestStatus) {
      const orcaStatusList = manifestStatus
        .split(",")
        .map((item) => item.trim());
      searchConditions.push({
        manifestDetails: {
          OR: orcaStatusList.map((status) => ({
            manifestStatus: {
              contains: status,
              mode: "insensitive",
            },
          })),
        },
      });
    }

    if (manifestDate_from || manifestDate_to) {
      const dateRangeFilter: any = {};
      if (manifestDate_from) {
        const fromDate = new Date(manifestDate_from);
        fromDate.setHours(0, 0, 0, 0);
        dateRangeFilter.gte = fromDate;
      }
      if (manifestDate_to) {
        const toDate = new Date(manifestDate_to);
        toDate.setHours(23, 59, 59, 999);
        dateRangeFilter.lte = toDate;
      }

      searchConditions.push({
        manifestDetails: {
            manifestDate: dateRangeFilter,
        },
      });
    }
    if (actionRequired) {
      const actionRequiredList = actionRequired
        .split(",")
        .map((item) => item.trim());
      searchConditions.push({
        manifestDetails: {
          OR: actionRequiredList.map((action) => ({
            actionRequired: {
              contains: action,
              mode: "insensitive",
            },
          })),
        },
      });
    }

    if (manifestNotes) {
      const manifestNotesList = manifestNotes
        .split(",")
        .map((item) => item.trim());
      searchConditions.push({
        manifestDetails: {
          OR: manifestNotesList.map((notes) => ({
            manifestNotes: {
              contains: notes,
              mode: "insensitive",
            },
          })),
        },
      });
    }

    if (freightTerm) {
      const freightTermList = freightTerm.split(",").map((item) => item.trim().toUpperCase());
      searchConditions.push({
        freightTerm: {
          in: freightTermList,
        },
      });
    }

    Object.keys(customFieldParams).forEach((key) => {
      if (
        key.startsWith("customField_") &&
        (key.endsWith("_from") || key.endsWith("_to"))
      ) {
        // Date range search for custom fields
        const match = key.match(/^customField_(.+)_(from|to)$/);
        if (match) {
          const fieldId = match[1];
          const bound = match[2];
          const fromKey = `customField_${fieldId}_from`;
          const toKey = `customField_${fieldId}_to`;
          const fromValue = customFieldParams[fromKey];
          const toValue = customFieldParams[toKey];
          if (fromValue || toValue) {
            const valueFilter: any = {};
            if (fromValue) {
              const fromDate = new Date(fromValue);
              fromDate.setHours(0, 0, 0, 0);
              valueFilter.gte = fromDate;
            }
            if (toValue) {
              const toDate = new Date(toValue);
              toDate.setHours(23, 59, 59, 999);
              valueFilter.lte = toDate;
            }
            searchConditions.push({
              TrackSheetCustomFieldMapping: {
                some: {
                  customField: { id: fieldId },
                  value: valueFilter,
                },
              },
            });
          }
        }
      } else if (
        key.startsWith("customField_") &&
        !key.endsWith("_from") &&
        !key.endsWith("_to")
      ) {
        const fieldId = key.replace("customField_", "");
        const searchValue = customFieldParams[key];
        if (searchValue && typeof searchValue === "string") {
          const valueList = searchValue.split(",").map((item) => item.trim());
          searchConditions.push({
            TrackSheetCustomFieldMapping: {
              some: {
                customField: { id: fieldId },
                OR: valueList.map((value) => ({
                  value: { contains: value, mode: "insensitive" },
                })),
              },
            },
          });
        }
      }
    });

    if (searchConditions.length > 0) {
      whereClause.AND = searchConditions;
    }

    const trackSheetWhere: any = {
      clientId: Number(clientId),
      ...(searchConditions.length > 0 ? { AND: searchConditions } : {}),
    };

    // Field mapping from frontend to backend
    const fieldMapping: { [key: string]: string } = {
      client: "client.client_name",
      company: "company",
      division: "division",
      masterInvoice: "masterInvoice",
      invoice: "invoice",
      bol: "bol",
      invoiceDate: "invoiceDate",
      receivedDate: "receivedDate",
      shipmentDate: "shipmentDate",
      carrier: "carrier.name",
      invoiceStatus: "invoiceStatus",
      manualMatching: "manualMatching",
      invoiceType: "invoiceType",
      currency: "currency",
      qtyShipped: "qtyShipped",
      weightUnitName: "weightUnitName",
      quantityBilledText: "quantityBilledText",
      freightClass: "freightClass",
      invoiceTotal: "invoiceTotal",
      savings: "savings",
      financialNotes: "financialNotes",
      ftpFileName: "ftpFileName",
      ftpPage: "ftpPage",
      filePath: "filePath",
      billToClient: "billToClient",
      docAvailable: "docAvailable",
      notes: "notes",
      mistake: "mistake",
      enteredBy: "enteredBy",
      shipperAddress: "shipperAddress",
      consigneeAddress: "consigneeAddress",
      billToAddress: "billToAddress",
      finalInvoice: "finalInvoice",
      otherDocuments: "otherDocuments",
      manifestStatus: "manifestDetails.manifestStatus",
      manifestDate: "manifestDetails.manifestDate",
      manifestNotes: "manifestDetails.manifestNotes",
      actionRequired: "manifestDetails.actionRequired",
      freightTerm: "freightTerm",
    };
    const sortByArr = String(sortBy)
      .split(",")
      .map((s) => s.trim());
    const orderArr = String(order)
      .split(",")
      .map((s) => s.trim());
    const mappedSortByArr = sortByArr.map((field) => {
  const lowerField = field.toLowerCase();
  const matchedKey = Object.keys(fieldMapping).find(
    (key) => key.toLowerCase() === lowerField
  );
  return matchedKey ? fieldMapping[matchedKey] : field;
});

    const allowedFields = [
      "id",
      "client.client_name",
      "company",
      "division",
      "masterInvoice",
      "invoice",
      "bol",
      "invoiceDate",
      "receivedDate",
      "shipmentDate",
      "carrier.name",
      "invoiceStatus",
      "manualMatching",
      "invoiceType",
      "currency",
      "qtyShipped",
      "weightUnitName"  ,
      "quantityBilledTest",
      "freightClass" ,
      "invoiceTotal" ,
      "savings" ,
      "financialNotes" ,
      "ftpFileName" ,
      "ftpPage" ,
      "filePath" ,
      "billToClient" ,
      "docAvailable" ,
      "notes" ,
      "mistake" ,
      "enteredBy" ,
      "shipperAddress",
      "consigneeAddress",
      "billToAddress",
      "finalInvoice",
      "otherDocuments ",
      "manifestDetails.manifestStatus",
      "manifestDetails.manifestDate",
      "manifestDetails.manifestNotes",
      "manifestDetails.actionRequired",
      "freightTerm",
      
    ];
    const orderBy = getOrderBy(mappedSortByArr, orderArr, allowedFields, "id", "desc");

// Apply hasTickets filter using workItemId relationship
if (hasTickets === 'true') {
  const ticketsWithWorkItemIds = await prisma.ticket.findMany({
    where: {
      deletedAt: null,
      pipeline: {
        deletedAt: null,
      },
    },
    select: {
      workItemId: true,
    },
  });
  const workItemIdsWithTickets = ticketsWithWorkItemIds.map(t => Number(t.workItemId));
  
  if (workItemIdsWithTickets.length > 0) {
    trackSheetWhere.id = {
      in: workItemIdsWithTickets,
    };
  } else {
    // If no tickets exist, return empty result
    trackSheetWhere.id = {
      in: [],
    };
  }
} else if (hasTickets === 'false') {
  const ticketsWithWorkItemIds = await prisma.ticket.findMany({
    where: {
      deletedAt: null,
      pipeline: {
        deletedAt: null,
      },
    },
    select: {
      workItemId: true,
    },
  });
  const workItemIdsWithTickets = ticketsWithWorkItemIds.map(t => Number(t.workItemId));
  
  if (workItemIdsWithTickets.length > 0) {
    trackSheetWhere.NOT = {
      id: {
        in: workItemIdsWithTickets,
      },
    };
  }
  // If no tickets exist, all tracksheets should be returned (no filter needed)
}
    const data = await prisma.trackSheets.findMany({
      where: trackSheetWhere,
      take: page ? take : undefined,
      skip: page ? skip : undefined,
      select: {
        id: true,
        clientId: true,
        company: true,
        division: true,
        masterInvoice: true,
        invoice: true,
        bol: true,
        invoiceDate: true,
        receivedDate: true,
        shipmentDate: true,
        invoiceStatus: true,
        manualMatching: true,
        invoiceType: true,
        currency: true,
        qtyShipped: true,
        weightUnitName: true,
        quantityBilledText: true,
        freightClass: true,
        invoiceTotal: true,
        savings: true,
        financialNotes: true,
        ftpFileName: true,
        ftpPage: true,
        filePath: true,
        billToClient: true,
        docAvailable: true,
        notes: true,
        mistake: true,
        shipperAddress: true,
        consigneeAddress: true,
        billToAddress: true,
        freightTerm:true,
        shipperAddressType:true,
        consigneeAddressType:true,
        billToAddressType:true,
        finalInvoice: true,
        otherDocuments: true,
        invoiceWarnings: true,
        manifestDetails : {
          select:{
            manifestStatus:true,
            manifestDate:true,
            manifestNotes:true,
            actionRequired:true
          }
        },
        enteredBy: true,
        createdAt: true,
        client: {
          select: {
            id: true,
            client_name: true,
          },
        },
        carrier: {
          select: {
            id: true,
            name: true,
          },
        },
        systemGeneratedWarnings: true,
        TrackSheetCustomFieldMapping: {
          select: {
            id: true,
            customFieldId: true,
            value: true,
          },
        },
      },
      orderBy,
    });

    // Fetch all tickets for the returned tracksheet IDs to check existence efficiently
    const trackSheetIds = data.map(item => item.id);
    const tickets = await prisma.ticket.findMany({
      where: {
        workItemId: { in: trackSheetIds.map(String)  },deletedAt: null,
        pipeline: {
          deletedAt: null,
        },
      },
      select: { workItemId: true, id: true }
    });
    const ticketMap = new Map(tickets.map(t => [t.workItemId, t.id]));

    // Transform the data to ensure decimal places are preserved and add ticketId if exists
    const transformedData = data.map(item => ({
      ...item,
      invoiceTotal: item.invoiceTotal ? Number(item.invoiceTotal).toFixed(2) : null,
      ticketId: ticketMap.get(String(item.id)) || null
    }));

    const datalength = await prisma.trackSheets.count({
      where: trackSheetWhere,
    });

    if (transformedData.length > 0) {
      return res.status(200).json({ data: transformedData, datalength });
    }
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};

export const ViewTrackSheetsById = async (req, res) => {
  const { id } = req.params;
  try {
    const data = await prisma.trackSheets.findUnique({
      where: { id: Number(id) },
      include: {
        client: true,
        carrier: true,
        systemGeneratedWarnings: true,
      },
    });
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(404).json({ message: "TrackSheet not found" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const getRecievedDatesByInvoice = async (req, res) => {
  try {
    const { invoice } = req.query;

    if (!invoice || invoice.length < 3) {
      return res
        .status(400)
        .json({ error: "Invoice search requires at least 3 characters" });
    }

    const data = await prisma.trackSheets.findMany({
      where: {
        invoice: {
          equals: invoice,
          mode: 'insensitive'
        }
      },
      select: {
        receivedDate: true,
        invoice: true,
      },
      orderBy: {
        receivedDate: "desc",
      },
    });

    if (data.length > 0) {
      return res.status(200).json(data);
    }
    return res.status(404).json({ message: "No matching invoices found" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const getInvoiceStats = async (req, res) => {
  try {
    const clientId = req.query.clientId
      ? Number(req.query.clientId)
      : undefined;
    if (!clientId) {
      return res.status(400).json({ error: "clientId is required" });
    }
    const where = { clientId };

    // Total invoices for this client
    const totalInvoices = await prisma.trackSheets.count({ where });

    // Today's invoices for this client
    const startOfToday = new Date();
    startOfToday.setHours(0, 0, 0, 0);
    const endOfToday = new Date();
    endOfToday.setHours(23, 59, 59, 999);

    const invoicesToday = await prisma.trackSheets.count({
      where: {
        ...where,
        createdAt: {
          gte: startOfToday,
          lte: endOfToday,
        },
      },
    });

    return res.json({
      totalInvoices,
      invoicesToday,
    });
  } catch (error) {
    handleError(res, error);
  }
};
