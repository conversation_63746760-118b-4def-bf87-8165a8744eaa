export function getOrderBy(
  sortByArr: string[],
  orderArr: string[],
  allowedFields: string[],
  defaultSortField?: string,
  defaultSortOrder?: "asc" | "desc"
) {
  if (!allowedFields.length) {
    throw new Error("No allowed fields defined for sorting.");
  }

  // Ensure arrays are properly handled
  if (!Array.isArray(sortByArr)) {
    sortByArr = sortByArr ? [String(sortByArr)] : [];
  }
  if (!Array.isArray(orderArr)) {
    orderArr = orderArr ? [String(orderArr)] : [];
  }

  // Filter out empty or invalid fields
  const validSortFields = sortByArr.filter(field => field && String(field).trim());

  if (validSortFields.length === 0) {
    // Use provided defaults or fallback to first allowed field with desc order
    const fallbackField = defaultSortField && allowedFields.includes(defaultSortField)
      ? defaultSortField
      : allowedFields[0];
    const fallbackOrder = defaultSortOrder || "desc";
    return [{ [fallbackField]: fallbackOrder }];
  }

  return validSortFields.map((rawField, idx) => {
    const inputField = String(rawField).trim().toLowerCase();
    const orderValue = orderArr[idx] ? String(orderArr[idx]).trim().toLowerCase() : "asc";
    const sortOrder = orderValue === "desc" ? "desc" : "asc";

    // Find matching allowed field in a case-insensitive way
    const matchedField =
      allowedFields.find((f) => f.toLowerCase() === inputField) || allowedFields[0];

    if (matchedField.includes(".")) {
      const [relation, nestedField] = matchedField.split(".");
      return {
        [relation]: {
          [nestedField]: sortOrder,
        },
      };
    }

    return { [matchedField]: sortOrder };
  });
}
