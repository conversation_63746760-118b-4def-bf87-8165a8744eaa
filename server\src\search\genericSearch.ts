import { getDMMF } from "@prisma/internals";
import { readdirSync, readFileSync } from "fs";
import path from "path";
import { getOrderBy } from "../utils/sortHelper";

interface GenericSearchParams {
  model: keyof typeof prisma;
  filters?: Record<string, any>;
  relatedFilters?: Record<string, Record<string, any>>;
  dateRange?: { field: string; from?: Date; to?: Date };
  extraConditions?: any[];
  page?: number;
  pageSize?: number;
  select?: object;
  userIdFilter?: number[]; // for user_id in []
  includeRelations?: boolean | string[];
  sortBy?: string;
  order?: "asc" | "desc";
}

type PrismaQueryOptions = {
  where?: any;
  take?: number;
  skip?: number;
  select?: any;
  include?: any;
  orderBy?: any;
};

// Cache schema metadata to avoid parsing on every request
let schemaCache: {
  modelFieldTypes: Record<string, Record<string, string>>;
  modelRelations: Record<string, string[]>;
  enumValues: Record<string, string[]>;
} | null = null;

const mergeSchemaFiles = (schemaDir: string): string => {
  const files = readdirSync(schemaDir).filter((file) =>
    file.endsWith(".prisma")
  );
  return files
    .map((file) => readFileSync(path.join(schemaDir, file), "utf-8"))
    .join("\n");
};

// Initialize the schema cache
const initSchemaCache = async () => {
  if (schemaCache) return schemaCache;
  try {
    const schemaDir = path.join(process.cwd(), "prisma", "schema");
    // const schemaPath = path.join(process.cwd(), "prisma/schema/schema.prisma");
    const schema = mergeSchemaFiles(schemaDir);
    // readFileSync

    const dmmf = await getDMMF({ datamodel: schema });

    const modelFieldTypes: Record<string, Record<string, string>> = {};
    const modelRelations: Record<string, string[]> = {};

    // Process all models
    dmmf.datamodel.models.forEach((model) => {
      const modelName = model.name.toLowerCase();
      modelFieldTypes[modelName] = {};
      modelRelations[modelName] = [];

      model.fields.forEach((field) => {
        modelFieldTypes[modelName][field.name] = field.type;

        if (field.kind === "object") {
          modelRelations[modelName].push(field.name);
        }
      });
    });

    // Process enum values
    const enumValues: Record<string, string[]> = {};
    dmmf.datamodel.enums.forEach((enumType) => {
      enumValues[enumType.name] = enumType.values.map((v) => v.name);
    });

    schemaCache = { modelFieldTypes, modelRelations, enumValues };
    return schemaCache;
  } catch (error) {
    console.error("Failed to initialize schema cache:", error);
    throw error;
  }
};

// Call this function when your application starts
export const initializePrismaCache = async () => {
  await initSchemaCache();
};

export const genericSearch = async ({
  model,
  filters = {},
  relatedFilters = {},
  dateRange,
  extraConditions = [],
  page,
  pageSize,
  select,
  userIdFilter,
  includeRelations,
  sortBy = "id",
  order = "desc",
}: GenericSearchParams) => {
  // Ensure schema cache is initialized
  if (!schemaCache) {
    await initSchemaCache();
  }

  const start = Date.now();
  const take = page && pageSize ? pageSize : undefined;
  const skip = page && pageSize ? (page - 1) * pageSize : undefined;
  const searchConditions: any[] = [];
  const normalizedModel = (model as string).toLowerCase();

  // Handle related filters
  for (const relation in relatedFilters) {
    const fields = relatedFilters[relation];
    for (const field in fields) {
      const value = fields[field];
      if (value === undefined || value === null || value === "") continue;
      const valueList = value.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: valueList.map((values) => ({
          [relation]: {
            [field]: {
              contains: values,
              mode: "insensitive",
            },
          },
        })),
      });
    }
  }

  // Handle date range filters
  if (dateRange?.from || dateRange?.to) {
    const dateCondition: any = { [dateRange.field]: {} };
    if (dateRange.from) dateCondition[dateRange.field].gte = dateRange.from;
    if (dateRange.to) dateCondition[dateRange.field].lte = dateRange.to;
    searchConditions.push(dateCondition);
  }

  // Get field types from cache
  const fieldTypeMap = schemaCache?.modelFieldTypes[normalizedModel] || {};
  const enumValues = schemaCache?.enumValues || {};

  // Process filters with cached type information
  for (const field in filters) {
    const value = filters[field];
    if (value === undefined || value === null || value === "") continue;

    const fieldType = fieldTypeMap[field];

    if (fieldType === "String") {
      const valueList = value.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: valueList.map((values) => ({
          [field]: {
            contains: values,
            mode: "insensitive",
          },
        })),
      });
    } else if (["Int", "Float", "Decimal"].includes(fieldType)) {
      const num = Number(value);
      if (!isNaN(num)) {
        searchConditions.push({ [field]: num });
      }
    } else if (enumValues[fieldType]) {
      // Optimized enum handling
      const possibleValues = enumValues[fieldType];
      const matchingEnumValues = possibleValues.filter((enumValue) =>
        enumValue.toLowerCase().includes(String(value).toLowerCase())
      );

      if (matchingEnumValues.length > 0) {
        searchConditions.push({
          [field]: {
            in: matchingEnumValues,
          },
        });
      } else {
        searchConditions.push({ [field]: value });
      }
    } else {
      searchConditions.push({ [field]: value });
    }
  }

  // Build the where clause
  const whereClause = {
    AND: [...searchConditions, ...extraConditions],
    ...(userIdFilter ? { user_id: { in: userIdFilter } } : {}),
  };

  // Prepare include object only if relations exist and are requested
  let include: Record<string, boolean> = {};

  if (includeRelations) {
    const modelRelations = schemaCache?.modelRelations[normalizedModel] || [];

    let relationsToInclude: string[];

    if (includeRelations === true) {
      // Include all relations
      relationsToInclude = modelRelations;
    } else {
      // Filter to valid relations for the model
      relationsToInclude = includeRelations.filter((rel) =>
        modelRelations.includes(rel)
      );
    }

    relationsToInclude.forEach((relation) => {
      include[relation] = true;
    });
  }

  // Build orderBy using the sortHelper function for consistency
  const modelRelations = schemaCache?.modelRelations[normalizedModel] || [];
  const modelFields = Object.keys(schemaCache?.modelFieldTypes[normalizedModel] || {});

  // Filter out relation fields from allowed fields for sorting
  const allowedFields = modelFields.filter(field => !modelRelations.includes(field));

  // Prepare sort arrays
  const sortByArr = typeof sortBy === "string" ? sortBy.split(",").map(s => s.trim()) : [String(sortBy)];
  const orderArr = typeof order === "string" ? order.split(",").map(s => s.trim()) : [String(order)];

  // Use the sortHelper function with fallback to "id" and "desc"
  const orderBy = getOrderBy(sortByArr, orderArr, allowedFields, "id", "desc");

  // Build query options
  let queryOptions: PrismaQueryOptions = {
    where: whereClause,
    take,
    skip,
    orderBy,
  };

  // Add either select or include, but not both
  if (select) {
    queryOptions.select = select;
  } else if (Object.keys(include).length > 0 && includeRelations) {
    queryOptions.include = include;
  }

  // Execute query
  const queryStart = Date.now();
  const data = await (prisma[model] as any).findMany(queryOptions);
  const queryTime = Date.now() - queryStart;

  const datalength = await (prisma[model] as any).count({ where: whereClause });

  return { data, datalength };
};